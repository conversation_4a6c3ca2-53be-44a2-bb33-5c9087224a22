<template>
  <div class="mail-page">
    <EmailList @email-selected="handleEmailSelected" :userInfo="userInfo" :tags="tags" :emailList="tableData"
      :archiveFolders="archiveFolders" :pagination="pagination" :autoLoad="false" @page-change="handlePageChange"
      data-parent-height="true" class="customer-email-list" />
    <el-drawer :title="selectedEmail ? selectedEmail.subject || '邮件详情' : '邮件详情'" :visible.sync="drawer"
      :direction="direction" :before-close="handleClose" size="40%" :modal="false" :show-close="true">
      <!-- 加载状态 -->
      <div v-if="detailLoading" class="loading-container">
        <div class="loading-spinner">
          <i class="el-icon-loading"></i>
          <span>正在加载邮件详情...</span>
        </div>
      </div>

      <!-- 邮件详情 -->
      <EmailDetails v-else-if="selectedEmailDetail" :selectedEmailDetail="selectedEmailDetail"
        :filteredEmails="[selectedEmailDetail]" :showTrackDetail="false" @reply="handleReply"
        @reply-all="handleReplyAll" @forward="handleForward" @archive="handleArchive" @tag="handleTag"
        @distribute="handleDistribute" @star="handleStar" @delete="handleDelete" @set-reminder="handleSetReminder"
        @navigate="handleNavigate" @fullscreen="handleFullscreen" @add-newclues="handleAddNewClues"
        @add-salesorder="handleAddSalesOrder" @view-new-tab="handleViewNewTab" @edit-draft="handleEditDraft"
        @remove-tag="handleRemoveTag" @remove-from-archive="handleRemoveFromArchive" />
      <div v-else class="no-email-selected">
        <div class="no-email-icon">📧</div>
        <div class="no-email-text">请选择一封邮件查看详情</div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCustomerMailsAPI, queryEmailDetailsAPI, saveEmailReadStatusAPI } from '@/api/crm/email'
import EmailList from '../email/components/EmailList.vue'
import EmailDetails from '../email/components/EmailDetails.vue'

export default {
  name: 'EmailExchange',
  components: {
    EmailList,
    EmailDetails
  },
  props: {
    // 从客户详情页面传入的客户数据
    detail: {
      type: Object,
      default: () => ({})
    },
    // 客户ID
    id: {
      type: [String, Number],
      default: ''
    },
    // CRM类型
    crmType: {
      type: String,
      default: 'customer'
    }
  },
  data() {
    return {
      filters: {
        customerName: '',
        receiver: '',
        dateRange: [],
        status: ''
      },
      drawer: false,
      direction: 'rtl',
      selectedEmail: null, // 当前选中的邮件（列表中的简单数据）
      selectedEmailDetail: null, // 当前选中邮件的详细数据
      detailLoading: false, // 邮件详情加载状态
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),

    // 获取客户邮箱地址
    customerEmailAddresses() {
      if (!this.detail) return []

      const emails = []

      // 从客户详情中获取邮箱地址
      if (this.detail.email) {
        emails.push(this.detail.email)
      }

      // 如果有其他邮箱字段，也可以添加
      if (this.detail.emailAddress) {
        emails.push(this.detail.emailAddress)
      }

      // 去重并过滤空值
      return [...new Set(emails.filter(email => email && email.trim()))]
    },

    // 获取客户名称
    customerName() {
      return this.detail?.customerName || this.detail?.name || ''
    },

    // 获取客户ID
    customerId() {
      return this.id || this.detail?.customerId || this.detail?.id || ''
    }
  },
  methods: {
    // 处理邮件选择事件
    handleEmailSelected(email) {
      console.log('选中邮件:', email)
      this.selectedEmail = email
      this.drawer = true

      // 清空之前的详情数据
      this.selectedEmailDetail = null

      // 根据邮件ID获取详细信息
      this.getEmailDetails(email.id)
    },

    // 获取邮件详情
    async getEmailDetails(emailId) {
      if (!emailId) {
        console.warn('邮件ID为空，无法获取详情')
        this.$message.error('邮件ID为空')
        return
      }
      this.detailLoading = true

      try {
        const response = await queryEmailDetailsAPI({ id: emailId })

        // 设置邮件详情数据
        this.selectedEmailDetail = response.data

        // 标记邮件为已读（如果需要）
        this.markEmailAsRead(emailId)

      } catch (error) {
        console.error('获取邮件详情失败:', error)
        this.$message.error('获取邮件详情失败，请稍后重试')

        // 清空详情数据，避免显示错误的内容
        this.selectedEmailDetail = null
      } finally {
        this.detailLoading = false
      }
    },

    // 标记邮件为已读
    async markEmailAsRead(emailId) {
      try {
        await saveEmailReadStatusAPI(emailId)
        console.log('邮件已标记为已读:', emailId)

        // 更新列表中的邮件状态
        if (this.selectedEmail) {
          this.selectedEmail.flagsSeen = true
        }
      } catch (error) {
        console.error('标记邮件已读失败:', error)
        // 这里不显示错误消息，因为不影响主要功能
      }
    },

    // 关闭drawer
    handleClose(done) {
      this.drawer = false
      this.selectedEmail = null
      this.selectedEmailDetail = null
      this.detailLoading = false
      if (done) {
        done()
      }
    },

    // 邮件操作事件处理
    handleReply(email) {
      console.log('回复邮件:', email)
      this.$message.info('回复功能开发中...')
    },

    handleReplyAll(email) {
      console.log('回复全部:', email)
      this.$message.info('回复全部功能开发中...')
    },

    handleForward(email) {
      console.log('转发邮件:', email)
      this.$message.info('转发功能开发中...')
    },

    handleArchive(email) {
      console.log('归档邮件:', email)
      this.$message.info('归档功能开发中...')
    },

    handleTag(email) {
      console.log('标记邮件:', email)
      this.$message.info('标记功能开发中...')
    },

    handleDistribute(email) {
      console.log('分发邮件:', email)
      this.$message.info('分发功能开发中...')
    },

    handleStar(email) {
      console.log('星标邮件:', email)
      this.$message.info('星标功能开发中...')
    },

    handleDelete(email) {
      console.log('删除邮件:', email)
      this.$message.info('删除功能开发中...')
    },

    handleSetReminder(email) {
      console.log('设置提醒:', email)
      this.$message.info('提醒功能开发中...')
    },

    handleNavigate(direction) {
      console.log('导航:', direction)
      this.$message.info('导航功能开发中...')
    },

    handleFullscreen(email) {
      console.log('全屏查看:', email)
      this.$message.info('全屏功能开发中...')
    },

    handleAddNewClues(email) {
      console.log('添加线索:', email)
      this.$message.info('添加线索功能开发中...')
    },

    handleAddSalesOrder(email) {
      console.log('添加销售订单:', email)
      this.$message.info('添加销售订单功能开发中...')
    },

    handleViewNewTab(email) {
      console.log('新标签页查看:', email)
      this.$message.info('新标签页功能开发中...')
    },

    handleEditDraft(email) {
      console.log('编辑草稿:', email)
      this.$message.info('编辑草稿功能开发中...')
    },

    handleRemoveTag(email, tagId) {
      console.log('移除标签:', email, tagId)
      this.$message.info('移除标签功能开发中...')
    },

    handleRemoveFromArchive(email) {
      console.log('从归档中移除:', email)
      this.$message.info('移除归档功能开发中...')
    },

    // 处理分页变化
    handlePageChange({ currentPage, pageSize }) {
      console.log('分页变化:', { currentPage, pageSize })
      this.pagination.currentPage = currentPage
      this.pagination.pageSize = pageSize
      this.fetchData()
    },

    // 获取数据
    fetchData() {
      // 构建查询参数
      const params = {
        current: this.pagination.currentPage,
        size: this.pagination.pageSize,
        condition: {}
      }

      if(this.detail && this.detail.status == 'inbox'){
        params.condition = {
          emailAddress:'',
          customerId:'',
          emailAddressList:[this.detail.sendEmailAddress]
        }
      }else if(this.detail && this.detail.status == 'sent'){
        params.condition = {
          emailAddress:'',
          customerId:'',
          emailAddressList: this.detail.toList.map(item => item.emailAddress)
        }
      }else{
        params.condition = {
          emailAddress:'',
          customerId:this.customerId,
          emailAddressList: []
        }
      }

      getCustomerMailsAPI(params)
        .then(res => {
          this.tableData = res.data.records || []
          this.tableData = this.tableData.map(email => ({
            ...email,
            sender: email.sendEmailAddress || '',
            time: email.sentTime || email.receivedTime,
            isStarred: email.isStarred || false,
            read: email.flagsSeen || false,
            subject: email.subject || '',
            content: email.content || '',
            receivedAddress: (email.toList && email.toList.length > 0) ? email.toList[0].emailAddress : '',
            cc: email.ccList || [],
            bcc: email.bccList || [],
            attachments: email.fileList || [],
            hasAttachment: email.fileList && email.fileList.length > 0,
            size: email.size || 0,
            folder: email.folder || '',
            tags: email.tags || [],
            receivedDate: email.receivedTime || '',
            sendDate: email.sentTime || '',
            customerId: email.customerId || null,
            showTrackDetailTips: false
          }))

          this.pagination.total = parseInt(res.data.total) || 0

          // 如果没有数据且有客户信息，显示提示
          if (this.tableData.length === 0 && this.customerName) {
            console.log(`未找到客户 ${this.customerName} 的往来邮件`)
          }
        })
        .catch(error => {
          console.error('获取邮件列表失败:', error)
          this.tableData = []
          this.pagination.total = 0
        })
    },

    // 初始化数据
    initData() {
      // 如果有客户信息，则获取对应的邮件数据
      if (this.customerId || this.customerEmailAddresses.length > 0 || this.detail) {
        this.fetchData()
      } else {
        console.log('没有客户信息，不加载邮件数据')
      }
    }
  },
  mounted() {
    this.initData()
  },
  watch: {
    // 监听客户数据变化，重新加载邮件数据
    detail: {
      handler() {
        this.initData()
      },
      deep: true
    },
    // 监听客户ID变化
    id() {
      this.initData()
    }
  }
}
</script>

<style scoped>
.mail-page {
  padding: 0 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

/* 确保EmailList组件能够正确占用空间 */
.mail-page>* {
  flex-shrink: 0;
}

/* EmailList组件占用剩余空间 */
.mail-page :deep(.email-list) {
  flex: 1;
  min-height: 0;
  height: calc(100vh - 40px);
  /* 减去页面padding */
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

/* 确保邮件列表区域可以滚动 */
.mail-page :deep(.email-group) {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: calc(100% - 70px);
  /* 减去分页高度 */
  max-height: calc(100% - 70px);
}

/* 确保分页固定在底部 */
.mail-page :deep(.email-pagination) {
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  height: 70px;
  min-height: 70px;
  max-height: 70px;
}

/* 客户邮件列表特定样式 */
.customer-email-list {
  height: 100%;
  max-height: calc(100vh - 40px);
  /* 减去页面padding */
}

.customer-email-list :deep(.email-group) {
  height: calc(100% - 70px);
  /* 减去分页高度 */
  max-height: calc(100% - 70px);
  border: 1px solid #e6e9ed;
  border-radius: 4px;
  background-color: #fff;
}

.customer-email-list :deep(.email-pagination) {
  background-color: #fafafa;
  border-top: 2px solid #409eff;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
}

/* 无数据状态优化 */
.customer-email-list :deep(.no-results) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
  font-size: 14px;
}

.customer-email-list :deep(.no-results::before) {
  content: '📧';
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.filter-form {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #606266;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner i {
  font-size: 32px;
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-spinner span {
  font-size: 14px;
  color: #909399;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 无邮件选中状态样式 */
.no-email-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  text-align: center;
}

.no-email-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-email-text {
  font-size: 16px;
  color: #909399;
}

/* Drawer 内容样式优化 */
:deep(.el-drawer__body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mail-page {
    padding: 10px;
  }

  :deep(.el-drawer) {
    width: 90% !important;
  }
}
</style>
