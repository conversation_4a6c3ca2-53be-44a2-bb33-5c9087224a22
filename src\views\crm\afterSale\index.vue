<template>
  <div v-loading="loading" class="main-container">
    <wk-page-header :title="$route.meta.title">
      <template slot="right">
        <el-button v-if="saveAuth" type="primary" @click="createClick">新建维修单</el-button>
      </template>
    </wk-page-header>
    <div class="content">
      <wk-table-header ref="tableHeader" v-bind="tableHeaderProps" :crm-type="crmType" :search.sync="search"
        :selection-list="tableSelectionList" :filter-header-props="tableHeaderProps.filterHeaderProps"
        @filter-change="handleFilter" @event-change="tableHeaderHandle" @operations-click="tableOperationsClick"
        :operations="handleOperations" />
      <el-table :data="list" height="600" :class="crmTableClass" :cell-class-name="cellClassName"
        :row-height="rowHeight" stripe border highlight-current-row style="width: 100%" @row-click="handleRowClick"
        @sort-change="sortChange" @selection-change="handleSelectionChange" type="expand" @expand-change="handleExpand"
        ref="mainTable">
        <el-table-column type="expand">
          <template slot-scope="props">
            <div style="padding: 20px;">
              <el-table :data="props.row.tableData" style="width: 100%">
                <el-table-column prop="customerName" label="客户名称" width="180">
                </el-table-column>
                <el-table-column prop="statusName" label="维修状态" width="120">
                </el-table-column>
                <el-table-column prop="servicerUserName" label="服务人" width="120">
                </el-table-column>
                <el-table-column prop="repairReason" label="维修原因">
                </el-table-column>
                <el-table-column prop="repairContent" label="维修内容">
                </el-table-column>
                <el-table-column prop="repairVerificationResult" label="验证结果">
                </el-table-column>
                <el-table-column prop="afterSaleDate" label="维修时间" width="150">
                </el-table-column>
                <el-table-column prop="remarks" label="备注" width="180">
                </el-table-column>
                <el-table-column v-if="!config.isSelect" fixed="right" label="操作" width="150">
                  <template slot-scope="scope">
                    <el-button v-if="$auth('crm.afterSale.update')" type="text" size="small"
                      @click.stop="handleEdit(scope.row)">编辑</el-button>
                    <el-button v-if="$auth('crm.afterSale.delete')" type="text" size="small"
                      @click.stop="handleDelete(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 子表格分页 -->
              <div class="sub-pagination" style="margin-top: 10px; text-align: right;">
                <el-pagination :current-page="props.row.subCurrentPage || 1" :page-sizes="[10, 20, 50, 100]"
                  :page-size="props.row.subPageSize || 15" :total="props.row.subTotal || 0"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="(val) => handleSubSizeChange(val, props.row)"
                  @current-change="(val) => handleSubCurrentChange(val, props.row)" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="模具编号" prop="productCode">
        </el-table-column>
        <el-table-column label="模具名称" prop="productName">
        </el-table-column>
        <el-table-column label="维修次数" prop="count">
        </el-table-column>
        <wk-empty slot="empty" :props="{
          buttonTitle: '新建维修单',
          showButton: saveAuth
        }" @click="createClick" />
      </el-table>
      <div class="p-contianer">
        <el-pagination :current-page="currentPage" :page-sizes="pageSizes" :page-size="pageSize" :total="total"
          class="p-bar" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新建/编辑 -->
    <create v-if="createVisible" :action="createAction" @close="createVisible = false" @save-success="handleHandle" />
  </div>
</template>

<script>
import { crmSaleRepairDeleteAPI, crmSaleRepairReadAPI, crmSaleRepairIndexAPI } from '@/api/crm/saleRepair'
import TableMixin from '../mixins/Table'
import Create from './Create'

export default {
  name: 'SaleRepair',
  components: {
    Create
  },
  mixins: [TableMixin],
  data() {
    return {
      crmType: 'afterSale',
      createVisible: false,
      createAction: {
        type: 'save',
        id: '',
        data: {}
      },
      tableData: []
    }
  },
  computed: {
    // 可操作选项
    handleOperations() {
      return this.getOperations([
        'delete',
      ])
    }
  },
  methods: {
    /**
     * 处理行点击事件
     */
    handleRowClick(row, column) {
      if (column.type === 'selection') {
        return // 多选布局不能点击
      }
    },

    /**
     * 编辑操作
     */
    handleEdit(row) {
      this.createAction = {
        type: 'update', // 更新操作类型
        id: row.afterSaleId,
        data: { ...row }
      }
      this.getDetial(row.afterSaleId);
      this.createVisible = true
    },

    handleExpand(row, expandedRows) {
      if (row._childrenLoaded) {
        return;
      }
      // 初始化子表格分页参数
      this.$set(row, 'subCurrentPage', 1);
      this.$set(row, 'subPageSize', 15);
      this.$set(row, 'subTotal', 0);

      this.loadSubTableData(row);
    },

    /**
     * 加载子表格数据
     */
    loadSubTableData(row) {
      this.$refs.mainTable.toggleRowExpansion(row, false);
      let params = {
        searchList: [
          {
            name: "productCode",
            type: "1",
            values: [
              row.productCode
            ]
          },
          {
            name: "productName",
            type: "1",
            values: [
              row.productName
            ]
          }
        ],
        type: 20,
        page: row.subCurrentPage || 1,
        limit: row.subPageSize || 15
      }
      crmSaleRepairIndexAPI(params).then((res) => {
        this.$set(row, 'tableData', res.data.list);
        this.$set(row, 'subTotal', res.data.totalRow);
        this.$set(row, '_childrenLoaded', true);
        this.$nextTick(() => {
          this.$refs.mainTable.toggleRowExpansion(row, true);
        });
      }).catch(() => {
        console.log('查询失败')
      })
    },

    /**
     * 子表格分页大小改变
     */
    handleSubSizeChange(val, row) {
      this.$set(row, 'subPageSize', val);
      this.$set(row, 'subCurrentPage', 1);
      this.loadSubTableData(row);
    },

    /**
     * 子表格当前页改变
     */
    handleSubCurrentChange(val, row) {
      this.$set(row, 'subCurrentPage', val);
      this.loadSubTableData(row);
    },

    /**
     * 删除操作
     */
    handleDelete(row) {
      this.$confirm('确定要删除该维修记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crmSaleRepairDeleteAPI([row.afterSaleId])
          .then(() => {
            this.$message.success('删除成功')
            this.refreshList()
          })
          .catch((error) => {
            // 处理删除失败的情况
            this.$message.error('删除失败：' + error.message)
          })
      }).catch((error) => {
        // 处理取消删除的情况,不需要做任何操作
        console.log('用户取消删除操作')
      })
    },
    tableOperationsClick(type) {
      if (type === 'delete') {
        this.$confirm(`确定删除选中的${this.selectionList.length}项吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.loading = true
            crmSaleRepairDeleteAPI(this.selectionList.map(item => item[`${this.crmType}Id`]))
              .then(res => {
                this.loading = false
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.handleHandle({ type })
              })
              .catch(() => {
                // 批量沟通 可能部分删除成功，需要刷新列表
                this.handleHandle({ type })
                this.loading = false
              })
          })
          .catch(() => { })
      }
    },

    /**
     * 批量删除
     */
    batchDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择要删除的维修记录')
        return
      }
      const ids = this.selectionList.map(item => item.afterSaleId)
      this.$confirm(`确定要删除选中的${ids.length}条维修记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crmSaleRepairDeleteAPI({ ids })
          .then(() => {
            this.$message.success('删除成功')
            this.refreshList()
          })
          .catch(() => { })
      }).catch(() => { })
    },
    getDetial(id) {
      this.loading = true
      crmSaleRepairReadAPI({
        afterSaleId: id
      })
        .then(res => {
          this.loading = false
          const resData = res.data || {}
          this.createAction.data.abnormalImages = res.data && res.data.abnormalPhotoList;
          this.createAction.data.verifyImages = res.data && res.data.verificationPhotoList;
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 单元格样式
     */
    cellClassName({ row, column }) {
      if (column.property === 'mouldNumber') {
        return 'can-visit--underline'
      } else {
        return ''
      }
    },

    /**
     * 新建维修单
     */
    createClick() {
      this.createAction = {
        type: 'save',
        id: '',
        data: {}
      }
      this.createVisible = true
    },
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/table.scss";

.main-container {
  height: 100%;
  overflow: hidden;
}

.content {
  position: relative;
  height: calc(100% - 50px);
  padding: 0 40px;
}

.p-contianer {
  position: relative;
  background-color: white;
  height: 44px;

  .p-bar {
    float: right;
    margin: 5px 0 0 0;
    font-size: 14px !important;
  }
}
</style>