<template>
  <el-select
    v-model="localEmails"
    multiple
    filterable
    :remote="true"
    allow-create
    default-first-option
    :remote-method="debouncedRemoteSearch"
    :loading="loading"
    :placeholder="placeholder"
    :disabled="disabled"
    class="multi-email-input"
    popper-class="multi-email-dropdown"
    @change="handleChange"
    @paste.native="handlePaste"
    @visible-change="handleDropdownVisibleChange"
  >
    <el-option
      v-for="(option) in filteredOptions"
      :label="option.email"
      :value="option.email"
    >
      <div class="option-item">
        <span class="option-avatar">
          <img :src="require('@/assets/img/head.png')" alt="用户头像"  style="width:25px; height: 25px;"/>
        </span>
        <span class="option-name">{{ option.name }}</span>
        <span class="option-email">{{ option.email }}</span>
        <span class="option-email">{{ option.type == 'customer'?'客户': option.type == 'contact'?'联系人':'陌生客户' }}</span>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import {
   getRecipientListAPI,findOtherAccountSentAPI
} from '@/api/crm/email'
import { debounce } from 'throttle-debounce'
export default {
  name: 'MultiEmailInput',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请输入邮箱地址，按回车确认或粘贴多个',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    composeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localEmails: this.processInitialValue(this.value),
      filteredOptions: [],
      loading: false,
    };
  },
  created() {
    this.debouncedRemoteSearch = debounce(500, this.remoteSearch);
  },
  computed:{
    userInfo() {
      return this.$store.state.user.userInfo
    },
  },
  watch: {
    value(newVal) {
      this.localEmails = this.processInitialValue(newVal);
    },
  },
  methods: {
    processInitialValue(value) {
      if (!value) return [];
      if (typeof value === 'string') {
        const fullStringMatch = value.match(/^(.+)<([^>]+)>$/);
        if (fullStringMatch && this.isValidEmail(fullStringMatch[2].trim())) {
          return [fullStringMatch[2].trim()];
        }
        const emailsArray = value.split(/[,;；，\s\n]+/)
          .map(email => {
            const matches = email.match(/<([^>]+)>/);
            return matches ? matches[1].trim() : email.trim();
          })
          .filter(email => email && this.isValidEmail(email));
        return Array.from(new Set(emailsArray));
      }
      if (Array.isArray(value)) {
        const validEmails = value
          .map(email => {
            if (typeof email === 'string') {
              const matches = email.match(/<([^>]+)>/);
              return matches ? matches[1].trim() : email.trim();
            }
            return '';
          })
          .filter(email => email && this.isValidEmail(email));
        return Array.from(new Set(validEmails));
      }
      return [];
    },
    async handleChange() {
      this.loading = true;
      const validEmails = this.localEmails.filter(this.isValidEmail);
      const emailStr = String(validEmails)
      let params = {
        emailAccountId:this.composeData.senderId,
        emailAddress:emailStr
      }
      try {
        const response = await findOtherAccountSentAPI(params);
        const result = response.data;
        if(result){
          this.$notify.error({
            title: '提示',
            message: `${this.composeData.userName} 发邮件冲突：联系人邮箱后缀 ${emailStr} 与 ${result.conflictUsername} 的客户冲突,客户编号：${result.conflictCustomerId}`,
            position: 'bottom-right',
            duration: 0
          });
          this.localEmails = Array.from(new Set(validEmails));
          this.$emit('input', this.localEmails);
          this.$emit('change', this.localEmails);
        }else{
          this.localEmails = Array.from(new Set(validEmails));
          this.$emit('input', this.localEmails);
          this.$emit('change', this.localEmails);
        }
      } catch (e) {
          this.localEmails = Array.from(new Set(validEmails));
          this.$emit('input', this.localEmails);
          this.$emit('change', this.localEmails);
      } finally {
        this.loading = false;
      }
    },
    handlePaste(event) {
      const paste = (event.clipboardData || window.clipboardData).getData('text');
      const fullStringMatch = paste.match(/^(.+)<([^>]+)>$/);
      if (fullStringMatch && this.isValidEmail(fullStringMatch[2].trim())) {
        const email = fullStringMatch[2].trim();
        if (!this.localEmails.includes(email)) {
          this.localEmails = [...this.localEmails, email];
          this.handleChange();
        }
        event.preventDefault();
        return;
      }
      const pastedEmails = paste.split(/[,;；，\s\n]+/)
        .map(email => {
          const matches = email.match(/<([^>]+)>/);
          return matches ? matches[1].trim() : email.trim();
        })
        .filter(email => email && this.isValidEmail(email));
      this.localEmails = Array.from(new Set([...this.localEmails, ...pastedEmails]));
      this.handleChange();
      event.preventDefault();
    },
    isValidEmail(email) {
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return regex.test(email);
    },
    async remoteSearch(query) {
      if (!query || query.length < 2) {
        this.filteredOptions = [];
        return;
      }
      this.loading = true;
      let params = {
        condition: {
          keywords: query
        },
        current: 1,
        size: 100
      }
      try {
        const response = await getRecipientListAPI(params);
        const result = response.data.records;
        const cleaned = result
          .filter(item => this.isValidEmail(item.email))
          .filter(item => !this.localEmails.includes(item.email))
          .map(item => ({
            name: item.name || '未知用户',
            email: item.email,
            type: item.type
          }));
        this.filteredOptions = cleaned;
      } catch (e) {
        console.error('搜索失败', e);
        this.filteredOptions = [];
      } finally {
        this.loading = false;
      }
    },
    handleDropdownVisibleChange(visible) {
      // 下拉框关闭时不自动清空选项，保持搜索结果
      // 只有在没有搜索内容时才清空
    },
  },
};
</script>

<style scoped>
.multi-email-input {
  width: 100% !important;
  display: block !important;
  box-sizing: border-box;
  overflow: hidden;
}

.multi-email-input :deep(.el-select) {
  width: 100% !important;
  display: block !important;
  overflow: hidden;
}

.multi-email-input :deep(.el-select__wrapper) {
  width: 100% !important;
  display: block !important;
}

.multi-email-input :deep(.el-input__wrapper) {
  width: 100% !important;
  display: flex !important;
}

.multi-email-input :deep(.el-input__inner) {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
  width: 100% !important;
  box-sizing: border-box;
  min-height: 38px;
  height: auto;
  line-height: 22px;
}

.multi-email-input :deep(.el-input__wrapper) {
  min-height: 38px;
  height: auto;
  align-items: flex-start;
  padding: 4px 8px;
}

.multi-email-input :deep(.el-select__wrapper) {
  min-height: 38px;
  height: auto;
  align-items: flex-start;
  padding: 4px 8px;
}

.multi-email-input :deep(.el-input__inner:focus) {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.multi-email-input :deep(.el-input__inner:hover) {
  border-color: #c0c4cc;
}

.multi-email-input :deep(.el-select__tags) {
  max-height: none;
  overflow: visible;
  margin: 0;
  padding: 0 5px;
  min-height: 30px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.multi-email-input :deep(.el-tag) {
  margin: 2px 4px 2px 0;
  background-color: #f0f2f5;
  border-color: #d9d9d9;
  color: #606266;
  border-radius: 4px;
  font-size: 12px;
  height: 22px;
  line-height: 20px;
  padding: 0 6px;
  display: inline-flex;
  align-items: center;
}

.multi-email-input :deep(.el-tag .el-tag__close) {
  color: #909399;
  font-size: 12px;
}

.multi-email-input :deep(.el-tag .el-tag__close:hover) {
  color: #606266;
  background-color: #c0c4cc;
}

.multi-email-input :deep(.el-input.is-focus .el-input__inner) {
  border-color: #409eff;
}

.multi-email-input :deep(.el-input) {
  width: 100% !important;
  display: block;
}

.multi-email-input :deep(.el-select .el-input) {
  width: 100% !important;
}

.multi-email-input :deep(.el-select__tags-text) {
  max-width: 100%;
}
.multi-email-input :deep(.el-select__input) {
  margin-left: 6px !important;
  color:#333 !important;
}

/* 选项样式 */
.option-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 8px;
}

.option-avatar {
  flex-shrink: 0;
  line-height: 20px;
}

.option-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  line-height: 1.2;
  flex-shrink: 0;
}

.option-email {
  color: #606266;
  font-size: 12px;
  line-height: 1.2;
  margin-left: auto;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
<style>
.multi-email-dropdown {
  width: auto !important;
  min-width: 400px !important;
  max-width: 600px;
}
</style>
