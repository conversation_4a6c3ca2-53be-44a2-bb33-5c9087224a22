<template>
  <div class="test-container">
    <h2>邮件详情组件样式测试</h2>

    <email-detail-content
      :email-detail="testEmailDetail"
      :detail-loading="false"
      :show-toolbar="true"
      :has-prev-email="true"
      :has-next-email="true"
      :compose-data="{}"
      :languages="testLanguages"
      :show-email-meta="true"
      :show-ai-summary="false"
      :is-generating-summary="false"
      :ai-summary-content="''"
      :show-sender-tooltip="false"
      :show-to-tooltip="false"
      :active-tooltip-index="-1"
      :active-tooltip-type="''"
      :tooltip-position="{x:0,y:0}"
      :customer-info="{}"
      :show-all-to="false"
      :show-all-cc="false"
      @reply="handleReply"
      @reply-all="handleReplyAll"
      @forward="handleForward"
      @archive="handleArchive"
      @tag="handleTag"
      @distribute="handleDistribute"
      @translate="handleTranslate"
      @star="handleStar"
      @delete="handleDelete"
      @set-reminder="handleSetReminder"
      @navigate="handleNavigate"
      @fullscreen="handleFullscreen"
      @add-newclues="handleAddNewclues"
      @add-salesorder="handleAddSalesorder"
      @edit-draft="handleEditDraft"
      @toggle-email-meta="handleToggleEmailMeta"
      @open-email-exchange="handleOpenEmailExchange"
      @remove-tag="handleRemoveTag"
      @copy-to-clipboard="handleCopyToClipboard"
      @sender-tooltip="handleSenderTooltip"
      @sender-leave="handleSenderLeave"
      @hide-sender-tooltip="handleHideSenderTooltip"
      @to-tooltip="handleToTooltip"
      @to-leave="handleToLeave"
      @hide-to-tooltip="handleHideToTooltip"
      @toggle-to-expand="handleToggleToExpand"
      @toggle-cc-expand="handleToggleCcExpand"
      @close-ai-summary="handleCloseAiSummary"
      @perform-translation="handlePerformTranslation"
      @toggle-original-view="handleToggleOriginalView"
      @close-translation="handleCloseTranslation"
      @preview-attachment="handlePreviewAttachment"
      @download-attachment="handleDownloadAttachment"
    />
  </div>
</template>

<script>
import EmailDetailContent from './EmailDetailContent.vue'

export default {
  name: 'EmailDetailContentTest',
  components: {
    EmailDetailContent
  },
  data() {
    return {
      testLanguages: [
        { code: 'zh', name: '中文' },
        { code: 'en', name: 'English' },
        { code: 'ja', name: '日本語' }
      ],
      testEmailDetail: {
        subject: '南海（2025）11号 关于印发佛山市南海区达有限公司《工程建设项目基本建设设计审评管理办法》、《工程变更审查管理办法》、《工程建设项目廉洁同步监督工作方案》的通知',
        sendEmailAddress: '<EMAIL>',
        sentTime: '2025/07/31 09:17',
        receivedTime: '2025/07/31 09:18',
        priority: 1,
        status: 'inbox',
        belongMailAccountAddress: '<EMAIL>',
        belongMailAccountUserName: '罗志脑',
        toList: [
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' }
        ],
        ccList: [
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' },
          { emailAddress: '<EMAIL>' }
        ],
        tagList: [
          { tagId: 1, tagName: '重要', colorClass: '#f50' },
          { tagId: 2, tagName: '工程', colorClass: '#2db7f5' }
        ],
        content: '<p>各位同事：</p><p>现将《工程建设项目基本建设设计审评管理办法》、《工程变更审查管理办法》、《工程建设项目廉洁同步监督工作方案》印发给你们，请认真贯彻执行。</p><p>特此通知。</p>',
        fileList: [
          {
            name: '工程建设项目基本建设设计审评管理办法.pdf',
            size: 1024000
          },
          {
            name: '工程变更审查管理办法.docx',
            size: 512000
          }
        ]
      }
    }
  },
  methods: {
    // 事件处理方法
    handleReply() { console.log('Reply') },
    handleReplyAll() { console.log('Reply All') },
    handleForward() { console.log('Forward') },
    handleArchive() { console.log('Archive') },
    handleTag() { console.log('Tag') },
    handleDistribute() { console.log('Distribute') },
    handleTranslate() { console.log('Translate') },
    handleStar() { console.log('Star') },
    handleDelete() { console.log('Delete') },
    handleSetReminder() { console.log('Set Reminder') },
    handleNavigate() { console.log('Navigate') },
    handleFullscreen() { console.log('Fullscreen') },
    handleAddNewclues() { console.log('Add New Clues') },
    handleAddSalesorder() { console.log('Add Sales Order') },
    handleEditDraft() { console.log('Edit Draft') },
    handleToggleEmailMeta() { console.log('Toggle Email Meta') },
    handleOpenEmailExchange() { console.log('Open Email Exchange') },
    handleRemoveTag() { console.log('Remove Tag') },
    handleCopyToClipboard() { console.log('Copy to Clipboard') },
    handleSenderTooltip() { console.log('Sender Tooltip') },
    handleSenderLeave() { console.log('Sender Leave') },
    handleHideSenderTooltip() { console.log('Hide Sender Tooltip') },
    handleToTooltip() { console.log('To Tooltip') },
    handleToLeave() { console.log('To Leave') },
    handleHideToTooltip() { console.log('Hide To Tooltip') },
    handleToggleToExpand() { console.log('Toggle To Expand') },
    handleToggleCcExpand() { console.log('Toggle Cc Expand') },
    handleCloseAiSummary() { console.log('Close AI Summary') },
    handlePerformTranslation() { console.log('Perform Translation') },
    handleToggleOriginalView() { console.log('Toggle Original View') },
    handleCloseTranslation() { console.log('Close Translation') },
    handlePreviewAttachment() { console.log('Preview Attachment') },
    handleDownloadAttachment() { console.log('Download Attachment') }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}
</style>
