<template>
  <div class="email-detail-container">
    <!-- 邮件头部信息 -->
    <div class="email-header">
      <div class="header-top">
        <h3 class="email-subject">{{ email.subject }}</h3>
      </div>
      <div class="email-recipients-exchange-row">
        <!-- 发给字段 -->
        <div class="email-recipients-info" v-if="email && email.toList && email.toList.length > 0">
          <span class="recipients-label">发给：</span>
          <span class="recipients-value">{{ getRecipientUsernames(email.toList) }}</span>
        </div>
      </div>
      <div class="email-send-status" v-if="email.status === 'sent'">
        <span class="recipients-label">发送状态：</span>
        <span class="recipients-value">
          {{ getSendStatusText(email.trackingStatus) }}
        </span>
      </div>
      <!-- 额外的邮件详细信息 -->
      <div class="email-meta email-details-section">
        <div class="email-details-container">
          <div class="email-info">
            <div class="sender-info">
              <span class="sender-name">发件人:</span>
              <span class="sender-email">&lt;{{ email.sendEmailAddress }}&gt;</span>
            </div>
            <div class="email-time">{{ email.sendTime }}</div>
          </div>
          <div class="recipients" v-if="email.toList && email.toList.length">
            <span class="sender-name">收件人：</span>
            <div class="recipients-list">
              <div v-for="(item, index) in (showAllTo ? email.toList : email.toList.slice(0, 20))" :key="index"
                class="recipient-item">
                <span class="email-address">{{ item.emailAddress }}</span>
              </div>
              <span v-if="email.toList.length > 20" class="more-recipients" @click="toggleToExpand">
                {{ showAllTo ? '收起' : `等${email.toList.length - 20}人` }}
              </span>
            </div>
          </div>
          <div class="recipients" v-if="email.ccList && email.ccList.length">
            <span class="sender-name">抄送：</span>
            <div class="recipients-list">
              <div v-for="(item, index) in (showAllCc ? email.ccList : email.ccList.slice(0, 10))" :key="index"
                class="recipient-item">
                <span class="email-address">{{ item.emailAddress }}</span>
              </div>
              <span v-if="email.ccList.length > 10" class="more-recipients" @click="toggleCcExpand">
                {{ showAllCc ? '收起' : `等${email.ccList.length - 10}人` }}
              </span>
            </div>
          </div>
          <div class="detail-group">
            <div class="detail-item">
              <div class="detail-label">发送时间：</div>
              <div class="detail-value">{{ email.sentTime || '-' }}</div>
            </div>
            <div class="detail-item" v-if="email.receivedTime">
              <div class="detail-label">接收时间：</div>
              <div class="detail-value">{{ email.receivedTime || '-' }}</div>
            </div>
            <div class="detail-item" v-else>
              <div class="detail-label"></div>
              <div class="detail-value"></div>
            </div>
            <div class="detail-item">
              <div class="detail-label">优先级：</div>
              <div class="detail-value">{{ email.priority == 1 ? '普通' : '一对一' }}</div>
            </div>
          </div>
          <div class="detail-group">
            <div class="detail-item">
              <div class="detail-label">目录：</div>
              <div class="detail-value">{{ email.status == 'inbox' ? '收件箱' : '发件箱' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">归属账号：</div>
              <div class="detail-value">{{ email.belongMailAccountAddress }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">拥有人：</div>
              <div class="detail-value">{{ email.belongMailAccountUserName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 附件列表 -->
    <div class="attachments" v-if="email.fileList && email.fileList.length">
      <h3>附件 ({{ email.fileList.length }})</h3>
      <div class="attachment-list">
        <div v-for="attachment in email.fileList" :key="attachment.id" class="attachment-item"
          @click="handleDownloadAttachment(attachment)">
          <i class="el-icon-paperclip"></i>
          <span class="attachment-name">{{ attachment.name }}</span>
        </div>
      </div>
    </div>

    <!-- 邮件内容 -->
    <div class="email-content">
      <div class="content-wrapper" v-html="email.content"></div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '@/utils/index'
import { queryEmailDetailsAPI } from '@/api/crm/email'

export default {
  name: 'EmailDetail',
  data() {
    return {
      email: {},
      // 抄送人展开/收起状态
      showAllCc: false,
      // 收件人展开/收起状态
      showAllTo: false,
    }
  },
  created() {
    this.loadEmailDetail()
  },
  watch: {
    // 监听路由变化，重新加载邮件详情
    '$route'(to, from) {
      if (to.params.id !== from.params.id) {
        this.loadEmailDetail()
      }
    }
  },
  methods: {
    loadEmailDetail() {
      const emailId = this.$route.params.id
      console.log('路由参数:', this.$route.params)
      if (emailId) {
        this.fetchEmailDetailFromAPI(emailId)
      }
    },
    getRecipientUsernames(toList) {
      if (!toList || toList.length === 0) return '';

      const maxDisplay = 5;
      const usernames = toList.map(recipient => {
        const email = recipient.emailAddress || recipient;
        return email.split('@')[0];
      });

      if (usernames.length <= maxDisplay) {
        return usernames.join('，');
      } else {
        const displayNames = usernames.slice(0, maxDisplay).join('，');
        const remainingCount = usernames.length - maxDisplay;
        return `${displayNames}，等${remainingCount}人`;
      }
    },
    // 切换抄送人展开/收起状态
    toggleCcExpand() {
      this.showAllCc = !this.showAllCc;
    },

    // 切换收件人展开/收起状态
    toggleToExpand() {
      this.showAllTo = !this.showAllTo;
    },

    // 通过API获取邮件详情
    fetchEmailDetailFromAPI(emailId) {
      console.log('开始获取邮件详情:', emailId)

      queryEmailDetailsAPI({ id: emailId })
        .then(res => {
          console.log('邮件详情获取成功:', res.data)

          // 处理邮件详情数据，参考index.vue的字段映射
          const emailDetail = {
            ...res.data,
            // 字段映射：API字段 -> 页面期望字段
            sender: res.data.sendEmailAddress || '',
            sendEmailAddress: res.data.sendEmailAddress || '',
            time: res.data.sentTime || res.data.receivedTime,
            sendTime: res.data.sentTime || res.data.receivedTime,
            isStarred: res.data.isStarred || false,
            read: res.data.flagsSeen || false,
            flagsSeen: res.data.flagsSeen || false,
            subject: res.data.subject || '',
            content: res.data.content || '',
            receivedAddress: (res.data.toList && res.data.toList.length > 0) ? res.data.toList[0].emailAddress : '',
            toList: res.data.toList || [],
            ccList: res.data.ccList || [],
            bccList: res.data.bccList || [],
            attachments: res.data.fileList || [],
            fileList: res.data.fileList || [],
            hasAttachment: res.data.fileList && res.data.fileList.length > 0,
            size: res.data.size || 0,
            folder: res.data.folder || '',
            tags: res.data.tags || [],
            receivedDate: res.data.receivedTime || '',
            receivedTime: res.data.receivedTime || '',
            sendDate: res.data.sentTime || '',
            sentTime: res.data.sentTime || '',
            customerId: res.data.customerId || null,
            customerName: res.data.customerName || ''
          }

          // 设置邮件详情
          this.email = emailDetail
          console.log('邮件详情设置完成:', this.email)
        })
        .catch(error => {
          console.error('获取邮件详情失败:', error)
          this.$message.error('获取邮件详情失败')
        })
    },

    handleReply() {
      const email = this.email

      // 设置收件人为原邮件的发件人
      let to = ''
      if (email.status === 'inbox') {
        to = email.sendEmailAddress + (email.tag ? ` <${email.tag}>` : '')
      } else if (email.status === 'sent') {
        to = email.toList ? email.toList.map(item => item.emailAddress).join(',') : ''
      }

      // 设置主题，添加"回复："前缀（如果没有的话）
      let subject = email.subject || ''
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject
      }

      // 设置回复内容，包含原文
      const quoteHeader = `
        <p>------------------ 原始邮件 ------------------</p>
        <p>发件人: ${email.sendEmailAddress || ''}${email.tag ? ` <${email.tag}>` : ''}</p>
        <p>发送时间: ${email.sendTime || email.sentTime || ''}</p>
        <p>主题: ${email.subject || ''}</p>
        <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(',') : ''}</p>
        <p>------------------------------------------</p>
      `

      const content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="padding-left: 10px; color: #666;">${email.content || ''}</blockquote>`

      // 跳转到写邮件页面
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: {
          to: to,
          subject: encodeURIComponent(subject),
          content: encodeURIComponent(content),
          replyMode: 'reply',
          originalEmailId: email.id
        }
      })
    },

    handleForward() {
      const email = this.email

      // 设置主题，添加"转发："前缀（如果没有的话）
      let subject = email.subject || ''
      if (!subject.startsWith('转发:') && !subject.startsWith('Fwd:')) {
        subject = '转发: ' + subject
      }

      // 设置转发内容，包含原文
      const forwardHeader = `
        <p>------------------ 转发邮件 ------------------</p>
        <p>发件人: ${email.sendEmailAddress || ''}${email.tag ? ` <${email.tag}>` : ''}</p>
        <p>发送时间: ${email.sendTime || email.sentTime || ''}</p>
        <p>主题: ${email.subject || ''}</p>
        <p>收件人: ${email.toList ? email.toList.map(item => item.emailAddress).join(',') : ''}</p>
        <p>------------------------------------------</p>
      `

      const content = `<p><br></p><p><br></p>${forwardHeader}<div>${email.content || ''}</div>`

      // 处理附件信息
      let attachments = ''
      if (email.fileList && email.fileList.length > 0) {
        attachments = encodeURIComponent(JSON.stringify(email.fileList))
      } else if (email.attachments && email.attachments.length > 0) {
        attachments = encodeURIComponent(JSON.stringify(email.attachments))
      }

      // 跳转到写邮件页面
      this.$router.push({
        path: '/crm/email/subs/compose',
        query: {
          to: '',
          subject: encodeURIComponent(subject),
          content: encodeURIComponent(content),
          replyMode: 'forward',
          originalEmailId: email.id,
          attachments: attachments
        }
      })
    },

    handleDownloadAttachment(attachment) {
      // 下载附件
      console.log('下载附件:', attachment)
    },

    formatTime(time) {
      return formatTime(time)
    },
    getSendStatusText(trackingStatus) {
      switch (trackingStatus) {
        case 'sent':
          return '已发送';
        case 'delivered':
          return '已送达';
        case 'opened':
          return '已打开';
        default:
          return '已发送';
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.email-detail-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}


.email-header {
  border-bottom: 1px solid #e4e7ed;
  padding: 20px;
  flex-shrink: 0;
  overflow-y: auto;
  max-height: 60vh; // 限制头部最大高度，防止占满整个屏幕

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .back-btn {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #409eff;
      font-size: 14px;

      &:hover {
        color: #66b1ff;
      }

      i {
        margin-right: 5px;
      }
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .email-meta {
    background-color: #f4f5f6;
    border-radius: 4px;
    margin: 6px 0;
    padding: 0;

    .email-subject {
      font-size: 24px;
      font-weight: 600;
      color: #000;
      margin: 0 0 15px 0;
      line-height: 1.4;
    }

    .email-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .sender-info {
        .sender-email {
          color: #909399;
          font-size: 14px;
        }
      }

      .email-time {
        color: #909399;
        font-size: 14px;
      }
    }

    .recipients {
      margin-bottom: 5px;
      font-size: 14px;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      line-height: 1.5;
      max-height: 200px; // 限制收件人列表最大高度
      overflow-y: auto; // 当内容超出时显示滚动条

      .more-recipients {
        color: #409eff;
        cursor: pointer;
        font-size: 14px;
        margin-left: 4px;
        padding: 2px 6px;
        border-radius: 4px;
        background-color: #ecf5ff;
        border: 1px solid #b3d8ff;
        transition: all 0.3s;
      }

      .more-recipients:hover {
        background-color: #409eff;
        color: white;
      }

      .label {
        color: #606266;
        font-weight: 500;
        margin-right: 8px;
        flex-shrink: 0;
        min-width: fit-content;
      }

      .recipients-list {
        color: #909399;
        flex: 1;
        min-width: 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        word-break: break-word; // 确保长邮箱地址能够换行

        .recipient-item {
          display: inline-flex;
          align-items: center;
          margin-bottom: 4px;
        }

        .email-address {
          word-break: break-all;
          word-wrap: break-word;
          white-space: normal;
          margin-right: 2px;
          padding: 2px 4px;
          background-color: #f5f7fa;
          border-radius: 3px;
        }
      }
    }

    .email-details-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      font-size: 14px;
      color: #606266;
      padding: 16px;
      max-height: 300px; // 限制详情容器最大高度
      overflow-y: auto; // 当内容超出时显示滚动条
    }

    .detail-group {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .detail-item {
      width: 48%;
      display: flex;
      align-items: flex-start;
    }

    .detail-label {
      min-width: 60px;
      color: #909399;
      font-weight: 500;
    }

    .detail-value {
      flex: 1;
      word-break: break-word;
    }
  }
}
.email-send-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.send-status-label {
  font-weight: 500;
  margin-right: 8px;
}

.email-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  min-height: 0; // 确保flex子项可以收缩

  .content-wrapper {
    line-height: 1.6;
    color: #303133;
    font-size: 14px;

    // 邮件内容样式
    ::v-deep {
      img {
        max-width: 100%;
        height: auto;
      }

      table {
        border-collapse: collapse;
        width: 100%;
        margin: 10px 0;
      }

      table,
      th,
      td {
        border: 1px solid #ddd;
      }

      th,
      td {
        padding: 8px;
        text-align: left;
      }

      blockquote {
        margin: 10px 0;
        color: #666;
      }
    }
  }
}

.attachments {
  border-top: 1px solid #e4e7ed;
  padding: 20px;
  flex-shrink: 0;
  max-height: 200px; // 限制附件区域最大高度
  overflow-y: auto; // 当附件过多时显示滚动条

  h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #303133;
  }

  .attachment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .attachment-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      i {
        margin-right: 8px;
        color: #909399;
      }

      .attachment-name {
        color: #303133;
        margin-right: 5px;
      }

      .attachment-size {
        color: #909399;
        font-size: 12px;
      }
    }
  }
}
</style>