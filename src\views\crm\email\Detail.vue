<template>
  <div class="email-detail-container">
    <!-- 邮件头部信息 -->
    <div class="email-header">
      <div class="header-top">
        <div class="back-btn" @click="handleGoBack">
          <i class="el-icon-arrow-left"></i>
          <span>返回</span>
        </div>
        <div class="header-actions">
          <el-button size="small" @click="handleReply">回复</el-button>
          <el-button size="small" @click="handleForward">转发</el-button>
          <el-button size="small" @click="handleDelete" type="danger">删除</el-button>
        </div>
      </div>
      
      <div class="email-meta">
        <h2 class="email-subject">{{ email.subject }}</h2>
        <div class="email-info">
          <div class="sender-info">
            <span class="sender-name">发件人:</span>
            <span class="sender-email">&lt;{{ email.sendEmailAddress }}&gt;</span>
          </div>
          <div class="email-time">{{ formatTime(email.sendTime) }}</div>
        </div>
        <div class="recipients" v-if="email.toList && email.toList.length">
          <span class="label">收件人：</span>
          <span class="recipients-list">{{ email.toList.map(item => item.emailAddress).join(",") }}</span>
        </div>
        <div class="recipients" v-if="email.cc && email.cc.length">
          <span class="label">抄送：</span>
          <span class="recipients-list">{{ email.cc.map(item => item.emailAddress).join(",")  }}</span>
        </div>
        <div class="recipients" v-if="email.bcc && email.bcc.length">
          <span class="label">密送：</span>
          <span class="recipients-list">{{ email.bcc.map(item => item.emailAddress).join(",")  }}</span>
        </div>
      </div>
    </div>

    <!-- 邮件内容 -->
    <div class="email-content">
      <div class="content-wrapper" v-html="email.content"></div>
    </div>

    <!-- 附件列表 -->
    <div class="attachments" v-if="email.fileList && email.fileList.length">
      <h3>附件 ({{ email.fileList.length }})</h3>
      <div class="attachment-list">
        <div 
          v-for="attachment in email.fileList" 
          :key="attachment.id"
          class="attachment-item"
          @click="handleDownloadAttachment(attachment)"
        >
          <i class="el-icon-paperclip"></i>
          <span class="attachment-name">{{ attachment.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '@/utils/index'

export default {
  name: 'EmailDetail',
  data() {
    return {
      email: {}
    }
  },
  created() {
    this.loadEmailDetail()
  },
  methods: {
    loadEmailDetail() {
      const emailId = this.$route.params.id
      console.log('路由参数:', this.$route.params)
      if (emailId) {
        // 从路由参数或者API获取邮件详情
        if (this.$route.params.email) {
          this.email = this.$route.params.email
          console.log('从路由参数获取邮件数据:', this.email)
        } else {
          // 如果没有传递邮件对象，可以通过API获取
          this.$message.error('获取邮件详情失败')
        }
      }
    },

    handleGoBack() {
      this.$router.go(-1)
    },

    handleReply() {
      this.$router.push({
        path: '/crm/email/compose',
        query: {
          type: 'reply',
          emailId: this.email.id
        }
      })
    },

    handleForward() {
      this.$router.push({
        path: '/crm/email/compose',
        query: {
          type: 'forward',
          emailId: this.email.id
        }
      })
    },

    handleDelete() {
      this.$confirm('确定要删除这封邮件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除API
        console.log('删除邮件:', this.email.id)
        this.$message.success('删除成功')
        this.handleGoBack()
      }).catch(() => {
        // 取消删除
      })
    },

    handleDownloadAttachment(attachment) {
      // 下载附件
      console.log('下载附件:', attachment)
    },

    formatTime(time) {
      return formatTime(time)
    }
  }
}
</script>

<style lang="scss" scoped>
.email-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.email-header {
  border-bottom: 1px solid #e4e7ed;
  padding: 20px;
  flex-shrink: 0;

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .back-btn {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #409eff;
      font-size: 14px;

      &:hover {
        color: #66b1ff;
      }

      i {
        margin-right: 5px;
      }
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .email-meta {
    .email-subject {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 15px 0;
      line-height: 1.4;
    }

    .email-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .sender-info {
        .sender-name {
          font-weight: 600;
          color: #303133;
          margin-right: 8px;
        }

        .sender-email {
          color: #909399;
          font-size: 14px;
        }
      }

      .email-time {
        color: #909399;
        font-size: 14px;
      }
    }

    .recipients {
      margin-bottom: 5px;
      font-size: 14px;

      .label {
        color: #606266;
        font-weight: 500;
        margin-right: 8px;
      }

      .recipients-list {
        color: #909399;
      }
    }
  }
}

.email-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  .content-wrapper {
    line-height: 1.6;
    color: #303133;
    font-size: 14px;

    // 邮件内容样式
    ::v-deep {
      img {
        max-width: 100%;
        height: auto;
      }

      table {
        border-collapse: collapse;
        width: 100%;
        margin: 10px 0;
      }

      table, th, td {
        border: 1px solid #ddd;
      }

      th, td {
        padding: 8px;
        text-align: left;
      }

      blockquote {
        border-left: 4px solid #ddd;
        margin: 10px 0;
        padding-left: 15px;
        color: #666;
      }
    }
  }
}

.attachments {
  border-top: 1px solid #e4e7ed;
  padding: 20px;
  flex-shrink: 0;

  h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #303133;
  }

  .attachment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .attachment-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      i {
        margin-right: 8px;
        color: #909399;
      }

      .attachment-name {
        color: #303133;
        margin-right: 5px;
      }

      .attachment-size {
        color: #909399;
        font-size: 12px;
      }
    }
  }
}
</style>