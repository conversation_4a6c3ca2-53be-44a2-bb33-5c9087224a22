<template>
  <div class="email-app">
    <!-- 主视图 -->
    <div v-if="currentView === 'inbox'" class="email-inbox">
      <!-- 左侧边栏 -->
      <div class="sidebar" v-if="activeTab === 'email'">
        <!-- 邮件/客户邮件切换标签 -->
        <div class="email-tabs">
          <div class="tab active" @click="switchTab('email')">邮件</div>
          <div class="tab" @click="switchTab('customer')">客户邮件</div>
        </div>

        <!-- 用户信息和账户切换 -->
        <div class="user-info">
          <div class="avatar">
            <img src="./assets/avatar.png" alt="用户头像" />
          </div>
          <!-- 改进的用户下拉选择器 -->
          <div class="username-dropdown">
            <div 
              class="username-trigger" 
              @click="handleToggleUserDropdown" 
              :class="{ 'switching': switchingAccount, 'has-error': hasAccountError }"
            >
              <span>{{ composeData.userName || '请选择账户' }}</span>
              <chevron-down-icon class="icon-small" :class="{ 'rotated': userDropdownOpen }" />
              <!-- 状态指示器 -->
              <div v-if="switchingAccount" class="status-indicator switching">
                <loader-icon class="icon-small spinning" />
              </div>
              <div v-else-if="!accountStatus.hasValidEmail" class="status-indicator warning">
                <alert-triangle-icon class="icon-small" />
              </div>
              <div v-else class="status-indicator success">
                <check-circle-icon class="icon-small" />
              </div>
            </div>

            <!-- 下拉菜单 -->
            <div class="username-dropdown-menu" v-if="userDropdownOpen && !switchingAccount">
              <!-- 我自己选项 -->
              <div class="username-dropdown-item myself-option" @click="handleSelectMyself">
                <div class="user-info-content">
                  <div class="user-name">
                    <user-icon class="icon-small myself-icon" />
                    我自己
                  </div>
                  <div class="user-status">
                    <span v-if="getUserEmailStatus()" class="status-badge success">邮箱正常</span>
                    <span v-else class="status-badge warning">未配置邮箱</span>
                  </div>
                </div>
              </div>

              <!-- 其他邮箱账户 -->
              <div 
                v-for="account in mailAccounts" 
                :key="account.id" 
                class="username-dropdown-item"
                @click="handleSelectAccount(account)"
                :class="{ 'disabled': !account.isValidEmail }"
              >
                <div class="user-info-content">
                  <div class="user-name">{{ account.userName }}</div>
                  <div class="user-email" v-if="account.isValidEmail">{{ account.currentEmailAddress }}</div>
                  <div class="user-status">
                    <span v-if="account.isValidEmail" class="status-badge success">正常</span>
                    <span v-else class="status-badge error">邮箱异常</span>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="mailAccounts.length === 0" class="username-dropdown-empty">
                <mail-icon class="icon-small" />
                暂无其他邮箱账号
              </div>
            </div>
          </div>
        </div>

        <!-- 写邮件按钮 -->
        <button 
          class="compose-btn" 
          @click="openCompose" 
          :disabled="!emailFlag.writeEmail"
          :title="!emailFlag.writeEmail ? '需要配置有效邮箱才能发送邮件' : '写邮件'"
        >
          <edit-icon class="icon-small" />
          写邮件
          <span v-if="!emailFlag.writeEmail" class="disabled-reason">需要配置邮箱</span>
        </button>

        <!-- 账户状态提示 -->
        <div v-if="!accountStatus.hasValidEmail" class="account-status-tip">
          <alert-circle-icon class="icon-small" />
          <div class="tip-content">
            <div class="tip-title">邮箱未配置</div>
            <div class="tip-description">当前账户功能受限</div>
          </div>
        </div>

        <!-- 侧边栏功能区域 -->
        <div class="sidebar-sections">
          <!-- 常用功能 -->
          <div class="sidebar-section">
            <div class="section-header" @click="toggleSection('common')">
              <chevron-right-icon v-if="!sections.common" class="icon-small" />
              <chevron-down-icon v-else class="icon-small" />
              常用功能
            </div>
            <div v-if="sections.common" class="section-content">
              <div class="sidebar-item" :class="{ active: activeFilter === 'isStarred' }" @click="filterBySpecial('isStarred')">
                <star-icon class="icon-small" />
                星标邮件
              </div>
              <div class="sidebar-item" :class="{ active: activeFilter === 'oneToOne' }" @click="filterBySpecial('oneToOne')">
                <users-icon class="icon-small" />
                一对一邮件
              </div>
              <div class="sidebar-item" :class="{ active: activeFilter === 'sendTrack' }" @click="filterBySpecial('sendTrack')">
                <clock-icon class="icon-small" />
                发件追踪
              </div>
            </div>
          </div>

          <!-- 我的全部账号 -->
          <div class="sidebar-section">
            <div class="section-header" @click="toggleSection('account')">
              <chevron-right-icon v-if="!sections.account" class="icon-small" />
              <chevron-down-icon v-else class="icon-small" />
              我的全部账号
            </div>
            <div v-if="sections.account" class="section-content">
              <div class="sidebar-item" :class="{ active: activeFolder === 'inbox' }" @click="filterByFolder('inbox')">
                <inbox-icon class="icon-small" />
                收件箱
              </div>
              <div class="sidebar-item" :class="{ active: activeFolder === 'sent' }" @click="filterByFolder('sent')">
                <send-icon class="icon-small" />
                已发件箱
              </div>
              <div class="sidebar-item" :class="{ active: activeFolder === 'draft' }" @click="filterByFolder('draft')">
                <file-text-icon class="icon-small" />
                草稿箱
              </div>
              <div class="sidebar-item" :class="{ active: activeFolder === 'sent_trash' }" @click="filterByFolder('sent_trash')">
                <trash-icon class="icon-small" />
                回收站
              </div>
              <div class="sidebar-item" :class="{ active: activeFolder === 'spam' }" @click="filterByFolder('spam')">
                <shield-icon class="icon-small" />
                垃圾邮件箱
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间邮件列表区域 -->
      <div class="email-list">
        <!-- 邮件列表头部 -->
        <div class="email-list-header">
          <div class="filter-info">
            <h2>{{ currentFilterName }}</h2>
            <span class="email-count" v-if="filteredEmails.length">{{ filteredEmails.length }} 封邮件</span>
          </div>
          <div class="header-actions">
            <button class="action-btn" @click="refreshEmailList" :disabled="listLoading" title="刷新">
              <refresh-icon class="icon-small" :class="{ 'spinning': listLoading }" />
            </button>
          </div>
        </div>

        <!-- 邮件列表加载状态 -->
        <div v-if="listLoading" class="loading-container">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">加载邮件中...</div>
          </div>
        </div>

        <!-- 邮件列表内容 -->
        <div v-else-if="filteredEmails.length > 0" class="email-items">
          <div 
            v-for="email in filteredEmails" 
            :key="email.id" 
            class="email-item"
            :class="{ 
              'unread': !email.flagsSeen, 
              'selected': selectedEmail && selectedEmail.id === email.id 
            }"
            @click="selectEmail(email)"
          >
            <div class="email-meta">
              <div class="sender">{{ email.sender || email.from }}</div>
              <div class="time">{{ formatEmailTime(email.sentTime || email.receivedTime) }}</div>
            </div>
            <div class="email-subject">{{ email.subject || '无主题' }}</div>
            <div class="email-preview">{{ email.preview || '' }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <mail-icon class="empty-icon" />
          <div class="empty-title">暂无邮件</div>
          <div class="empty-description">
            <span v-if="!accountStatus.hasValidEmail">请先配置邮箱账户</span>
            <span v-else>当前筛选条件下没有邮件</span>
          </div>
        </div>
      </div>

      <!-- 右侧邮件详情 -->
      <div class="email-detail" v-if="selectedEmail">
        <div class="detail-header">
          <h3>{{ selectedEmail.subject || '无主题' }}</h3>
          <button class="close-detail" @click="closeEmailDetail">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="detail-content">
          <!-- 邮件详情内容 -->
          <div class="email-info">
            <div class="info-row">
              <strong>发件人:</strong> {{ selectedEmail.sender || selectedEmail.from }}
            </div>
            <div class="info-row">
              <strong>时间:</strong> {{ formatEmailTime(selectedEmail.sentTime || selectedEmail.receivedTime) }}
            </div>
          </div>
          <div class="email-body" v-html="selectedEmail.content || selectedEmail.htmlBody"></div>
        </div>
      </div>
    </div>

    <!-- 邮件编写组件 -->
    <EmailComposer 
      v-if="showComposer"
      @close="handleComposeClose" 
      :initial-data="composeData" 
      :compose-mode="composeMode"
      :email-flag="emailFlag"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import EmailComposer from './components/EmailComposer.vue'
import { queryMailAccountPageListAPI, getEmailListAPI } from '@/api/crm/email'

export default {
  name: 'EmailApp',
  components: {
    EmailComposer
  },
  data() {
    return {
      // === 基础状态 ===
      currentView: 'inbox',
      activeTab: 'email',
      listLoading: false,
      userDropdownOpen: false,
      
      // === 改进的账户管理状态 ===
      switchingAccount: false, // 账户切换loading状态
      hasAccountError: false,  // 账户错误状态
      mailAccounts: [],
      existsEmail: false,
      
      // === 账户状态管理 ===
      accountStatus: {
        hasValidEmail: false,
        currentAccount: null,
        lastSwitchTime: null,
        availableFeatures: [],
        validationErrors: []
      },
      
      // === 邮件相关状态 ===
      emailFlag: {},
      selectedEmail: null,
      filteredEmails: [],
      
      // === 邮件编写数据 ===
      composeData: {
        from: '',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: [],
        userName: '',
        userId: '',
        senderId: null
      },
      composeMode: 'new',
      showComposer: false,
      
      // === 筛选和导航状态 ===
      activeFolder: 'inbox',
      activeFilter: null,
      sections: {
        common: true,
        account: true,
        query: false,
        tags: false,
        archives: false
      }
    }
  },

  async mounted() {
    try {
      const { email } = this.crm;
      this.emailFlag = email;
      await this.initializeComponent();
    } catch (error) {
      console.error('组件挂载失败:', error);
      this.$message.error('邮件应用初始化失败');
    }
  },

  computed: {
    ...mapGetters(['userInfo', 'crm']),
    
    // 当前筛选名称
    currentFilterName() {
      if (this.activeFilter) {
        return this.getFilterDisplayName(this.activeFilter);
      }
      if (this.activeFolder) {
        return this.getFolderDisplayName(this.activeFolder);
      }
      return '收件箱';
    }
  },

  methods: {
    // ========== 邮箱验证方法 ==========
    
    /**
     * 验证邮箱地址格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmailAddress(email) {
      if (!email || typeof email !== 'string') {
        return false;
      }
      
      const trimmedEmail = email.trim();
      if (!trimmedEmail) {
        return false;
      }
      
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(trimmedEmail);
    },

    /**
     * 验证账户信息完整性
     * @param {Object} account - 账户对象
     * @param {boolean} requireEmail - 是否必须有邮箱
     * @returns {Object} 验证结果
     */
    validateAccountCompleteness(account, requireEmail = true) {
      if (!account) {
        return { valid: false, message: '账户信息不存在' };
      }

      if (!account.userName && !account.realname) {
        return { valid: false, message: '账户缺少用户名信息' };
      }

      if (requireEmail) {
        if (!account.currentEmailAddress) {
          return { valid: false, message: '账户未配置邮箱地址' };
        }
        
        if (!this.validateEmailAddress(account.currentEmailAddress)) {
          return { valid: false, message: '邮箱地址格式无效' };
        }
      }

      return { valid: true, message: '验证通过' };
    },

    /**
     * 获取当前用户的邮箱状态
     * @returns {boolean} 是否有有效邮箱
     */
    getUserEmailStatus() {
      if (!this.userInfo) return false;
      
      const userAccount = this.mailAccounts.find(
        account => account.userId === this.userInfo.userId && account.isValidEmail
      );
      
      return !!userAccount;
    },

    // ========== 改进的账户切换方法 ==========
    
    /**
     * 修复后的账户选择方法
     * @param {Object} account - 选择的账户
     * @returns {Promise<boolean>} 切换是否成功
     */
    async handleSelectAccount(account) {
      console.log('切换邮箱账号:', account);

      // 防止重复点击
      if (this.switchingAccount) {
        return false;
      }

      // 验证账户信息
      const validation = this.validateAccountCompleteness(account, true);
      if (!validation.valid) {
        this.$message.error(validation.message);
        this.hasAccountError = true;
        setTimeout(() => { this.hasAccountError = false; }, 3000);
        return false;
      }

      try {
        // 设置切换状态
        this.switchingAccount = true;
        this.hasAccountError = false;
        
        // 更新账户信息
        this.updateComposeDataForUser(account);
        
        // 更新UI状态
        this.userDropdownOpen = false;
        this.resetViewState();
        
        // 更新功能权限
        this.updateAccountStatus(account, true);
        
        // 安全地刷新邮件列表
        await this.refreshEmailListSafely();
        
        // 显示成功提示
        this.$message.success(`已切换到账户: ${account.userName}`);
        
        return true;
      } catch (error) {
        console.error('账户切换失败:', error);
        this.$message.error('账户切换失败，请重试');
        this.hasAccountError = true;
        return false;
      } finally {
        this.switchingAccount = false;
      }
    },

    /**
     * 修复后的"我自己"选择方法
     * @returns {Promise<boolean>} 切换是否成功
     */
    async handleSelectMyself() {
      console.log('选择我自己，当前用户信息:', this.userInfo);

      if (!this.userInfo) {
        this.$message.warning('无法获取当前用户信息');
        return false;
      }

      // 查找当前用户对应的邮箱账户
      const currentUserAccount = this.mailAccounts.find(
        account => account.userId === this.userInfo.userId
      );

      if (currentUserAccount) {
        // 验证找到的账户
        const validation = this.validateAccountCompleteness(currentUserAccount, true);
        if (validation.valid) {
          console.log('找到当前用户的有效邮箱账户:', currentUserAccount);
          return await this.handleSelectAccount(currentUserAccount);
        } else {
          // 账户存在但邮箱无效
          this.handleInvalidEmailAccount(currentUserAccount, validation.message);
          return false;
        }
      } else {
        // 没有找到邮箱账户
        this.handleNoEmailAccount();
        return false;
      }
    },

    /**
     * 处理无邮箱账户的情况
     */
    handleNoEmailAccount() {
      // 更新用户信息（无邮箱版本）
      this.updateComposeDataForUser({
        currentEmailAddress: '',
        userName: this.userInfo.realname || '当前用户',
        userId: this.userInfo.userId,
        id: null
      });

      this.userDropdownOpen = false;
      this.resetViewState();
      
      // 更新功能状态（受限模式）
      this.updateAccountStatus(null, false);
      
      // 显示详细提示
      this.showAccountLimitationMessage('noEmail');
    },

    /**
     * 处理邮箱配置无效的情况
     * @param {Object} account - 账户信息
     * @param {string} errorMessage - 错误消息
     */
    handleInvalidEmailAccount(account, errorMessage) {
      // 更新基本信息但不设置邮箱
      this.updateComposeDataForUser({
        currentEmailAddress: '',
        userName: account.userName || account.realname || '当前用户',
        userId: account.userId,
        id: account.id
      });

      this.userDropdownOpen = false;
      this.resetViewState();
      
      // 更新功能状态
      this.updateAccountStatus(account, false);
      
      // 显示错误信息
      this.$message.error(`邮箱配置异常: ${errorMessage}`);
      this.showAccountLimitationMessage('invalidEmail', errorMessage);
    },

    /**
     * 显示账户限制提示消息
     * @param {string} type - 限制类型
     * @param {string} details - 详细信息
     */
    showAccountLimitationMessage(type, details = '') {
      let title, message;
      
      if (type === 'noEmail') {
        title = '邮箱账户未配置';
        message = `当前账户尚未配置邮箱，功能受限：
        
✅ 可用功能：
• 查看邮件（如有权限）
• 搜索和筛选邮件

❌ 受限功能：
• 发送邮件
• 回复邮件
• 接收邮件同步

请联系管理员配置邮箱账户。`;
      } else if (type === 'invalidEmail') {
        title = '邮箱配置异常';
        message = `${details}

账户存在但邮箱配置有误，请联系管理员检查邮箱设置。`;
      }

      this.$message({
        type: 'warning',
        title,
        message,
        duration: 6000,
        showClose: true
      });
    },

    /**
     * 安全的邮件列表刷新方法
     */
    async refreshEmailListSafely() {
      try {
        // 检查是否有有效的邮箱账户
        if (!this.composeData.from || !this.validateEmailAddress(this.composeData.from)) {
          console.warn('无有效邮箱账户，跳过邮件列表刷新');
          this.filteredEmails = [];
          return;
        }
        
        await this.refreshEmailList();
      } catch (error) {
        console.error('刷新邮件列表失败:', error);
        this.$message.warning('邮件列表刷新失败，请稍后重试');
        throw error;
      }
    },

    /**
     * 更新账户状态的统一方法
     * @param {Object} account - 账户信息
     * @param {boolean} hasValidEmail - 是否有有效邮箱
     */
    updateAccountStatus(account, hasValidEmail) {
      // 更新邮件功能状态
      this.emailFlag.writeEmail = hasValidEmail;
      
      // 更新账户状态对象
      this.accountStatus = {
        hasValidEmail,
        currentAccount: account,
        lastSwitchTime: Date.now(),
        availableFeatures: this.getAvailableFeatures(hasValidEmail),
        validationErrors: []
      };
      
      console.log('账户状态已更新:', this.accountStatus);
    },

    /**
     * 获取可用功能列表
     * @param {boolean} hasValidEmail - 是否有有效邮箱
     * @returns {Array} 功能列表
     */
    getAvailableFeatures(hasValidEmail) {
      const baseFeatures = ['查看邮件', '搜索邮件', '邮件筛选'];
      const emailFeatures = ['发送邮件', '回复邮件', '转发邮件', '邮件同步'];
      
      return hasValidEmail ? [...baseFeatures, ...emailFeatures] : baseFeatures;
    },

    // ========== 改进的初始化方法 ==========
    
    /**
     * 组件初始化方法
     */
    async initializeComponent() {
      try {
        await this.getList();
        
        console.log(`成功加载 ${this.mailAccounts.length} 个邮箱账户`);
        
        // 如果存在有效邮箱，加载邮件
        if (this.existsEmail) {
          await this.refreshEmailList();
        }
      } catch (error) {
        console.error('组件初始化失败:', error);
        this.$message.error('系统初始化失败，请刷新页面');
      }
    },

    /**
     * 改进的获取邮箱账户列表方法
     */
    async getList() {
      try {
        this.listLoading = true;
        
        const params = {
          current: 1,
          pages: 1000,
          type: '',
          smtp: '',
          userId: '',
          emailAddress: ''
        };

        const res = await queryMailAccountPageListAPI(params);
        const { records } = res.data;

        // 处理每个账户的邮箱地址
        records.forEach(item => {
          const emailAddress = item.outgoingAuthType == 2 
            ? item.sendEmailAddress 
            : item.emailAddress;
          
          // 确保邮箱地址格式正确
          item.currentEmailAddress = this.validateEmailAddress(emailAddress) 
            ? emailAddress.trim() 
            : '';
            
          // 添加状态标识
          item.isValidEmail = !!item.currentEmailAddress;
        });

        this.mailAccounts = records;

        // 检查当前用户是否有邮箱账户
        this.existsEmail = records.some(
          item => item.userId === this.userInfo.userId && item.isValidEmail
        );

        // 设置初始账户状态
        this.initializeAccountState(records);

      } catch (error) {
        console.error('获取邮箱账户列表失败:', error);
        this.$message.error('获取邮箱账户失败，请刷新页面重试');
        throw error;
      } finally {
        this.listLoading = false;
      }
    },

    /**
     * 初始化账户状态的方法
     * @param {Array} records - 账户记录列表
     */
    initializeAccountState(records) {
      if (!this.existsEmail) {
        // 用户没有有效邮箱账户
        this.composeData.userName = this.userInfo.realname;
        this.composeData.userId = this.userInfo.userId;
        this.composeData.from = '';
        this.emailFlag.writeEmail = false;
        
        this.updateAccountStatus(null, false);
        console.log('用户无有效邮箱账户，限制功能访问');
      } else {
        // 查找用户的有效邮箱账户
        const matched = records.find(
          item => item.userId === this.userInfo.userId && item.isValidEmail
        );
        
        if (matched) {
          this.composeData.from = matched.currentEmailAddress;
          this.composeData.userName = matched.userName;
          this.composeData.userId = this.userInfo.userId;
          this.composeData.senderId = matched.id;
          this.emailFlag.writeEmail = true;
          
          this.updateAccountStatus(matched, true);
          console.log('用户有效邮箱账户初始化完成:', matched);
        }
      }
    },

    // ========== 辅助方法 ==========
    
    /**
     * 更新composeData的方法
     * @param {Object} account - 账户信息
     */
    updateComposeDataForUser(account) {
      if (!account) {
        console.warn('updateComposeDataForUser: account参数为空');
        return;
      }
      
      this.composeData.from = account.currentEmailAddress || '';
      this.composeData.userName = account.userName || account.realname || '未知用户';
      this.composeData.userId = account.userId;
      this.composeData.senderId = account.id;
      
      console.log('composeData已更新:', {
        from: this.composeData.from,
        userName: this.composeData.userName
      });
    },

    /**
     * 重置视图状态
     */
    resetViewState() {
      this.selectedEmail = null;
      this.activeFolder = 'inbox';
      this.activeFilter = null;
    },

    /**
     * 处理用户下拉菜单的展开/收起
     */
    handleToggleUserDropdown() {
      if (this.switchingAccount) {
        return;
      }
      this.userDropdownOpen = !this.userDropdownOpen;
    },

    /**
     * 切换侧边栏区域
     */
    toggleSection(section) {
      this.sections[section] = !this.sections[section];
    },

    /**
     * 切换标签页
     */
    switchTab(tab) {
      this.activeTab = tab;
    },

    /**
     * 按文件夹筛选
     */
    filterByFolder(folder) {
      this.activeFolder = folder;
      this.activeFilter = null;
      this.refreshEmailList();
    },

    /**
     * 按特殊条件筛选
     */
    filterBySpecial(filter) {
      this.activeFilter = filter;
      this.activeFolder = null;
      this.refreshEmailList();
    },

    /**
     * 获取筛选条件显示名称
     */
    getFilterDisplayName(filter) {
      const filterNames = {
        'unread': '未读邮件',
        'isStarred': '星标邮件',
        'oneToOne': '一对一邮件',
        'sendTrack': '发件追踪'
      };
      return filterNames[filter] || filter;
    },

    /**
     * 获取文件夹显示名称
     */
    getFolderDisplayName(folder) {
      const folderNames = {
        'inbox': '收件箱',
        'sent': '已发件箱',
        'draft': '草稿箱',
        'sent_trash': '回收站',
        'spam': '垃圾邮件箱'
      };
      return folderNames[folder] || folder;
    },

    /**
     * 打开邮件编写窗口
     */
    openCompose() {
      if (!this.emailFlag.writeEmail) {
        this.$message.warning('当前账户未配置邮箱，无法发送邮件');
        return;
      }
      this.showComposer = true;
    },

    /**
     * 关闭邮件编写窗口
     */
    handleComposeClose() {
      this.showComposer = false;
    },

    /**
     * 选择邮件
     */
    selectEmail(email) {
      this.selectedEmail = email;
    },

    /**
     * 关闭邮件详情
     */
    closeEmailDetail() {
      this.selectedEmail = null;
    },

    /**
     * 刷新邮件列表
     */
    async refreshEmailList() {
      if (!this.composeData.from) {
        this.filteredEmails = [];
        return;
      }

      try {
        this.listLoading = true;
        
        // 这里应该调用实际的邮件列表API
        // const response = await getEmailListAPI(params);
        // this.filteredEmails = response.data.records;
        
        // 模拟数据
        this.filteredEmails = [];
        
      } catch (error) {
        console.error('刷新邮件列表失败:', error);
        this.$message.error('刷新邮件列表失败');
      } finally {
        this.listLoading = false;
      }
    },

    /**
     * 格式化邮件时间
     */
    formatEmailTime(time) {
      if (!time) return '';
      // 这里实现时间格式化逻辑
      return new Date(time).toLocaleString();
    }
  }
};
</script>

<style scoped>
/* ========== 基础布局 ========== */
.email-app {
  display: flex;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: #f5f7fa;
}

.email-inbox {
  display: flex;
  width: 100%;
  height: 100%;
}

/* ========== 侧边栏样式 ========== */
.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.email-tabs {
  display: flex;
  border-bottom: 1px solid #e1e5e9;
}

.tab {
  flex: 1;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab.active {
  background: #0969da;
  color: white;
}

/* ========== 改进的用户信息区域 ========== */
.user-info {
  padding: 16px;
  border-bottom: 1px solid #e1e5e9;
}

.avatar {
  text-align: center;
  margin-bottom: 12px;
}

.avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

/* ========== 改进的账户切换器 ========== */
.username-dropdown {
  position: relative;
}

.username-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: white;
  border: 2px solid #d0d7de;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
}

.username-trigger:hover {
  border-color: #0969da;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
}

.username-trigger.switching {
  opacity: 0.7;
  cursor: not-allowed;
  border-color: #f59e0b;
}

.username-trigger.has-error {
  border-color: #dc2626;
  background: #fef2f2;
}

/* 状态指示器 */
.status-indicator {
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.status-indicator.switching .icon-small {
  color: #f59e0b;
}

.status-indicator.warning .icon-small {
  color: #f59e0b;
}

.status-indicator.success .icon-small {
  color: #16a34a;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 下拉菜单 */
.username-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
  z-index: 100;
  max-height: 300px;
  overflow-y: auto;
}

.username-dropdown-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f6f8fa;
  transition: background-color 0.2s ease;
}

.username-dropdown-item:hover {
  background: #f6f8fa;
}

.username-dropdown-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.username-dropdown-item.myself-option {
  background: #f0f9ff;
  border-bottom: 2px solid #e1e5e9;
}

.user-info-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.user-email {
  font-size: 12px;
  color: #656d76;
}

.user-status {
  display: flex;
  align-items: center;
}

/* 状态徽章 */
.status-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 12px;
  font-weight: 500;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.username-dropdown-empty {
  padding: 16px;
  text-align: center;
  color: #656d76;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* ========== 写邮件按钮 ========== */
.compose-btn {
  margin: 16px;
  padding: 12px 16px;
  background: #0969da;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.compose-btn:hover:not(:disabled) {
  background: #0550ae;
  transform: translateY(-1px);
}

.compose-btn:disabled {
  background: #8b949e;
  cursor: not-allowed;
  opacity: 0.6;
}

.disabled-reason {
  font-size: 10px;
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  color: #656d76;
}

/* ========== 账户状态提示 ========== */
.account-status-tip {
  margin: 16px;
  padding: 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-weight: 500;
  color: #856404;
  margin-bottom: 2px;
}

.tip-description {
  color: #856404;
}

/* ========== 侧边栏功能区域 ========== */
.sidebar-sections {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.sidebar-section {
  margin-bottom: 8px;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
  color: #24292f;
  font-size: 14px;
}

.section-header:hover {
  background: #f6f8fa;
}

.section-content {
  padding-left: 8px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  color: #656d76;
  transition: all 0.2s ease;
  font-size: 14px;
}

.sidebar-item:hover {
  background: #f6f8fa;
  color: #24292f;
}

.sidebar-item.active {
  background: #0969da;
  color: white;
}

/* ========== 邮件列表区域 ========== */
.email-list {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.email-list-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.filter-info h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #24292f;
}

.email-count {
  font-size: 14px;
  color: #656d76;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px;
  background: transparent;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f6f8fa;
}

/* ========== 加载状态 ========== */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0969da;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

.loading-text {
  color: #656d76;
  font-size: 14px;
}

/* ========== 邮件列表 ========== */
.email-items {
  flex: 1;
  overflow-y: auto;
}

.email-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f6f8fa;
  cursor: pointer;
  transition: all 0.2s ease;
}

.email-item:hover {
  background: #f6f8fa;
}

.email-item.selected {
  background: #e7f3ff;
  border-left: 3px solid #0969da;
}

.email-item.unread {
  background: #f8f9fa;
  font-weight: 500;
}

.email-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.sender {
  font-weight: 500;
  color: #24292f;
}

.time {
  font-size: 12px;
  color: #656d76;
}

.email-subject {
  color: #24292f;
  margin-bottom: 4px;
  font-size: 14px;
}

.email-preview {
  color: #656d76;
  font-size: 12px;
  line-height: 1.4;
}

/* ========== 空状态 ========== */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #656d76;
  text-align: center;
  padding: 40px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #24292f;
}

.empty-description {
  font-size: 14px;
  line-height: 1.5;
}

/* ========== 邮件详情 ========== */
.email-detail {
  flex: 1;
  background: white;
  border-left: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #24292f;
}

.close-detail {
  padding: 4px;
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: 4px;
}

.close-detail:hover {
  background: #f6f8fa;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.email-info {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e1e5e9;
}

.info-row {
  margin-bottom: 8px;
  font-size: 14px;
  color: #24292f;
}

.email-body {
  line-height: 1.6;
  color: #24292f;
}

/* ========== 通用图标样式 ========== */
.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.icon-tiny {
  width: 12px;
  height: 12px;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
  }
  
  .email-list-header {
    padding: 12px 16px;
  }
  
  .email-item {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .email-inbox {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    max-height: 50vh;
  }
  
  .compose-btn {
    margin: 12px;
    padding: 10px 14px;
  }
}
</style>
